import type { Metada<PERSON> } from "next";

import LogoIcon from "@/components/logo-icon";
import ThemeSwitcher from "@/components/theme-switcher";

export const metadata: Metadata = {
  title: "Authentication",
  description: "Sign in or create an account",
};

export default function AuthLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <div className="flex min-h-screen items-center justify-center px-4 sm:px-6 lg:px-8">
      <div className="w-full max-w-[380px] space-y-8">
        <div className="mx-auto flex w-fit">
          <LogoIcon />
        </div>

        {children}
      </div>
      <ThemeSwitcher triggerClassName="absolute top-6 right-6" />
    </div>
  );
}
