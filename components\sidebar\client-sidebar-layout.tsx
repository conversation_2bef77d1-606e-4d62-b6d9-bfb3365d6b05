"use client";

import { RiSkipRightLine } from "@remixicon/react";

import { Session } from "@/auth";
import { Separator } from "@/components/ui/separator";
import { SidebarTrigger } from "@/components/ui/sidebar";
import UserAvatar from "@/components/user-avatar";

interface ClientSidebarLayoutProps {
  children: React.ReactNode;
  session: Session;
}

export default function ClientSidebarLayout({
  children,
  session,
}: ClientSidebarLayoutProps) {
  return (
    <>
      <header className="bg-background sticky top-0 z-50 flex shrink-0 items-center gap-2 border-b p-4 transition-[margin-right] duration-200 ease-in-out">
        <SidebarTrigger className="-ml-1">
          <RiSkipRightLine className="!size-4" />
        </SidebarTrigger>

        <Separator
          orientation="vertical"
          className="mr-2 data-[orientation=vertical]:h-4"
        />

        <div className="ml-auto flex flex-row items-center gap-3">
          <UserAvatar session={session} />
        </div>
      </header>
      <main className="relative flex flex-1 flex-col gap-4 overflow-y-auto p-4 transition-[margin-right] duration-200 ease-in-out md:p-6 lg:p-8">
        {children}
      </main>
    </>
  );
}
