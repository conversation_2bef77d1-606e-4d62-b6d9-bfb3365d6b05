import Link from "next/link";

import LogoIcon from "@/components/logo-icon";
import { cn } from "@/lib/utils";

interface LogoProps {
  className?: string;
}

export default function Logo({ className }: LogoProps) {
  return (
    <Link
      href="/"
      className={cn("flex items-center space-x-2.5", className)}
    >
      <LogoIcon />

      <span className="text-foreground/90 !font-dm-sans text-xl font-medium tracking-tighter capitalize select-none">
        Next Core
      </span>
    </Link>
  );
}
